<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>core</artifactId>
    <groupId>io.dataease</groupId>
    <version>${dataease.version}</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <packaging>jar</packaging>

  <artifactId>core-frontend</artifactId>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <frontend-maven-plugin.version>1.9.1</frontend-maven-plugin.version>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-clean-plugin</artifactId>
        <configuration>
          <filesets>
            <!--<fileset>
              <directory>dist</directory>
            </fileset>-->
            <fileset>
                <directory>node_modules</directory>
            </fileset>
            <fileset>
                <directory>./</directory>
                <includes>
                    <include>*-lock.json</include>
                </includes>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.eirslett</groupId>
        <artifactId>frontend-maven-plugin</artifactId>
        <version>${frontend-maven-plugin.version}</version>
        <executions>
          <!--首次打包需要放开-->
          <execution>
            <id>install node and npm</id>
            <goals>
              <goal>install-node-and-npm</goal>
            </goals>
            <configuration>
              <nodeVersion>v23.11.0</nodeVersion>
              <npmVersion>10.9.2</npmVersion>
            </configuration>
          </execution>
          <!--前端组件有更新需要放开-->
           <execution>
            <id>npm install</id>
            <goals>
              <goal>npm</goal>
            </goals>
            <configuration>
              <arguments>install</arguments>
            </configuration>
          </execution>

          <execution>
            <id>npm run build</id>
            <goals>
              <goal>npm</goal>
            </goals>
            <configuration>
              <arguments>run build:distributed</arguments>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>


</project>
