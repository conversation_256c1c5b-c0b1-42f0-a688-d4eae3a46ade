{"name": "dataease", "private": true, "version": "0.0.0", "scripts": {"dev": "NODE_OPTIONS=--max_old_space_size=4096 vite --mode dev --host 0.0.0.0", "build:flush": "cd ./flushbonading && rimraf ./demo.html && npm i && node ./index.js", "ts:check": "vue-tsc --noEmit", "build:base": "NODE_OPTIONS=--max_old_space_size=4096 vite build --mode base && npm run build:flush", "build:distributed": "NODE_OPTIONS=--max_old_space_size=5020 vite build --mode distributed && npm run build:flush", "build:lib": "vite build --mode lib", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "preview": "vite preview"}, "dependencies": {"@antv/g2plot": "^2.4.29", "@antv/l7": "^2.22.0", "@antv/l7plot": "^0.5.5", "@antv/s2": "^1.49.0", "@babel/runtime": "^7.5.5", "@codemirror/lang-sql": "^6.4.0", "@npkg/tinymce-plugins": "^0.0.7", "@tinymce/tinymce-vue": "^5.1.0", "@turf/centroid": "^7.0.0", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^9.13.0", "ace-builds": "^1.15.3", "axios": "^1.3.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "decimal.js": "^10.5.0", "echarts": "^5.5.1", "element-plus-secondary": "^1.0.0", "element-resize-detector": "^1.2.4", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "js-base64": "^3.7.5", "jsencrypt": "3.3.2", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mathjs": "^11.6.0", "mitt": "^3.0.0", "net": "^1.0.2", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.0.32", "qs": "^6.11.0", "screenfull": "^6.0.2", "snowflake-id": "^1.1.0", "tinymce": "^5.8.2", "vant": "^4.8.3", "video.js": "^7.21.6", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-draggable-next": "^2.2.1", "vue-i18n": "^9.2.2", "vue-router": "4.1.3", "vue-router_2": "^4.1.3", "vue-types": "^5.0.2", "vue-uuid": "^3.0.0", "vue3-ace-editor": "^2.2.2", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xss": "^1.0.14"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^0.8.2", "@types/element-resize-detector": "^1.1.3", "@types/jquery": "^3.5.16", "@types/lodash-es": "^4.17.6", "@types/sockjs-client": "^1.5.4", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.13", "consola": "^2.15.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "jquery": "^3.6.4", "less": "^4.1.3", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.4", "rimraf": "^4.1.2", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "stylelint": "^15.2.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^10.0.1", "stylelint-config-standard": "^30.0.1", "stylelint-order": "^6.0.2", "stylelint-prettier": "^2.0.0", "typescript": "^4.9.3", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components-secondary": "^0.24.6", "vite": "^4.1.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-style-import-secondary": "^2.0.0", "vite-plugin-stylelint": "^4.2.0", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.0.24", "xss": "^1.0.14"}, "overrides": {"@intlify/message-compiler": "9.14.5", "@intlify/shared": "9.14.5", "glob": {"inflight": "npm:inflight-lru@^1.0.0"}}}