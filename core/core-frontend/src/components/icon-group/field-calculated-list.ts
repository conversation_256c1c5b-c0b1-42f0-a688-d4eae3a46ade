import icon_link_calculated_outlined from '@/assets/svg/icon_link-calculated_outlined.svg'
import icon_link_calculated_outlined_1 from '@/assets/svg/icon_link-calculated_outlined-1.svg'
import icon_text_calculated_outlined from '@/assets/svg/icon_text-calculated_outlined.svg'
import icon_text_calculated_outlined_1 from '@/assets/svg/icon_text-calculated_outlined-1.svg'
import icon_number_calculated_outlined from '@/assets/svg/icon_number-calculated_outlined.svg'
import icon_number_calculated_outlined_1 from '@/assets/svg/icon_number-calculated_outlined-1.svg'
import icon_local_calculated_outlined from '@/assets/svg/icon_local-calculated_outlined.svg'
import icon_local_calculated_outlined_1 from '@/assets/svg/icon_local-calculated_outlined-1.svg'
import icon_calendar_calculated_outlined from '@/assets/svg/icon_calendar-calculated_outlined.svg'
import icon_calendar_calculated_outlined_1 from '@/assets/svg/icon_calendar-calculated_outlined-1.svg'
const iconFieldCalculatedMap = [
  icon_text_calculated_outlined,
  icon_calendar_calculated_outlined,
  icon_number_calculated_outlined,
  icon_number_calculated_outlined,
  icon_number_calculated_outlined,
  icon_local_calculated_outlined,
  'binary',
  icon_link_calculated_outlined
]

const iconFieldCalculatedQMap = [
  icon_text_calculated_outlined_1,
  icon_calendar_calculated_outlined_1,
  icon_number_calculated_outlined_1,
  icon_number_calculated_outlined_1,
  icon_number_calculated_outlined_1,
  icon_local_calculated_outlined_1,
  'binary',
  icon_link_calculated_outlined_1
]

export { iconFieldCalculatedMap, iconFieldCalculatedQMap }
