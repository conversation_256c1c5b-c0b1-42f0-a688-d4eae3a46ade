import areaStack from '@/assets/svg/area-stack.svg'
import area from '@/assets/svg/area.svg'
import barGroupStack from '@/assets/svg/bar-group-stack.svg'
import barGroup from '@/assets/svg/bar-group.svg'
import barHorizontal from '@/assets/svg/bar-horizontal.svg'
import barRange from '@/assets/svg/bar-range.svg'
import barStackHorizontal from '@/assets/svg/bar-stack-horizontal.svg'
import barStack from '@/assets/svg/bar-stack.svg'
import bar from '@/assets/svg/bar.svg'
import bidirectionalBar from '@/assets/svg/bidirectional-bar.svg'
import bubbleMap from '@/assets/svg/bubble-map.svg'
import chartMixGroup from '@/assets/svg/chart-mix-group.svg'
import chartMixStack from '@/assets/svg/chart-mix-stack.svg'
import chartMixDualLine from '@/assets/svg/chart-mix-dual-line.svg'
import chartMix from '@/assets/svg/chart-mix.svg'
import flowMap from '@/assets/svg/flow-map.svg'
import funnel from '@/assets/svg/funnel.svg'
import gauge from '@/assets/svg/gauge.svg'
import heatMap from '@/assets/svg/heat-map.svg'
import indicator from '@/assets/svg/indicator.svg'
import line from '@/assets/svg/line.svg'
import liquid from '@/assets/svg/liquid.svg'
import map from '@/assets/svg/map.svg'
import percentageBarStackHorizontal from '@/assets/svg/percentage-bar-stack-horizontal.svg'
import percentageBarStack from '@/assets/svg/percentage-bar-stack.svg'
import pieDonutRose from '@/assets/svg/pie-donut-rose.svg'
import pieDonut from '@/assets/svg/pie-donut.svg'
import pieRose from '@/assets/svg/pie-rose.svg'
import pie from '@/assets/svg/pie.svg'
import progressBar from '@/assets/svg/progress-bar.svg'
import quadrant from '@/assets/svg/quadrant.svg'
import radar from '@/assets/svg/radar.svg'
import richText from '@/assets/svg/rich-text.svg'
import sankey from '@/assets/svg/sankey.svg'
import scatter from '@/assets/svg/scatter.svg'
import stockLine from '@/assets/svg/stock-line.svg'
import symbolicMap from '@/assets/svg/symbolic-map.svg'
import tableInfo from '@/assets/svg/table-info.svg'
import tableNormal from '@/assets/svg/table-normal.svg'
import tablePivot from '@/assets/svg/table-pivot.svg'
import treemap from '@/assets/svg/treemap.svg'
import waterfall from '@/assets/svg/waterfall.svg'
import wordCloud from '@/assets/svg/word-cloud.svg'
import tHeatmap from '@/assets/svg/t-heatmap.svg'
import pictureGroup from '@/assets/svg/picture-group.svg'
import filter from '@/assets/svg/filter.svg'
import outerParams from '@/assets/svg/icon_params_setting.svg'
import circlePacking from '@/assets/svg/circle-packing.svg'
import bulletGraph from '@/assets/svg/bullet-graph.svg'

const iconChartMap = {
  'area-stack': areaStack,
  area: area,
  'bar-group-stack': barGroupStack,
  'bar-group': barGroup,
  'bar-horizontal': barHorizontal,
  'bar-range': barRange,
  'bar-stack-horizontal': barStackHorizontal,
  'bar-stack': barStack,
  bar: bar,
  'bidirectional-bar': bidirectionalBar,
  'bubble-map': bubbleMap,
  'chart-mix-group': chartMixGroup,
  'chart-mix-stack': chartMixStack,
  'chart-mix-dual-line': chartMixDualLine,
  'chart-mix': chartMix,
  'flow-map': flowMap,
  funnel: funnel,
  gauge: gauge,
  'heat-map': heatMap,
  indicator: indicator,
  line: line,
  liquid: liquid,
  map: map,
  'percentage-bar-stack-horizontal': percentageBarStackHorizontal,
  'percentage-bar-stack': percentageBarStack,
  'pie-donut-rose': pieDonutRose,
  'pie-donut': pieDonut,
  'pie-rose': pieRose,
  pie: pie,
  'progress-bar': progressBar,
  quadrant: quadrant,
  radar: radar,
  'rich-text': richText,
  sankey: sankey,
  scatter: scatter,
  'stock-line': stockLine,
  'symbolic-map': symbolicMap,
  'table-info': tableInfo,
  'table-normal': tableNormal,
  'table-pivot': tablePivot,
  treemap: treemap,
  waterfall: waterfall,
  'word-cloud': wordCloud,
  't-heatmap': tHeatmap,
  'picture-group': pictureGroup,
  filter: filter,
  outerParams: outerParams,
  'circle-packing': circlePacking,
  'bullet-graph': bulletGraph
}

export { iconChartMap }
