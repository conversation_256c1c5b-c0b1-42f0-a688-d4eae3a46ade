import mysqlDs from '@/assets/svg/mysql-ds.svg'
import oracleDs from '@/assets/svg/oracle-ds.svg'
import sqlServerDs from '@/assets/svg/sqlServer-ds.svg'
import TiDBDs from '@/assets/svg/TiDB-ds.svg'
import impalaDs from '@/assets/svg/impala-ds.svg'
import mariadbDs from '@/assets/svg/mariadb-ds.svg'
import StarRocksDs from '@/assets/svg/StarRocks-ds.svg'
import pgDs from '@/assets/svg/pg-ds.svg'
import mongoDs from '@/assets/svg/mongo-ds.svg'
import ckDs from '@/assets/svg/ck-ds.svg'
import db2Ds from '@/assets/svg/db2-ds.svg'
import redshiftDs from '@/assets/svg/redshift-ds.svg'
import APIDs from '@/assets/svg/API-ds.svg'
import ExcelDs from '@/assets/svg/Excel-ds.svg'
import ExcelRemoteDs from '@/assets/svg/Excel-remote-ds.svg'
import dorisDs from '@/assets/svg/doris-ds.svg'
import esDs from '@/assets/svg/es-ds.svg'
const iconDatasourceMap = {
  mysql: mysqlDs,
  oracle: oracleDs,
  sqlServer: sqlServerDs,
  TiDB: TiDBDs,
  impala: impalaDs,
  mariadb: mariadbDs,
  StarRocks: StarRocksDs,
  pg: pgDs,
  mongo: mongoDs,
  ck: ckDs,
  db2: db2Ds,
  redshift: redshiftDs,
  API: APIDs,
  Excel: ExcelDs,
  ExcelRemote: ExcelRemoteDs,
  doris: dorisDs,
  es: esDs
}

export { iconDatasourceMap }
