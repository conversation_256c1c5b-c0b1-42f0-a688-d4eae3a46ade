<p align="center"><a href="https://dataease.io"><img src="https://dataease.oss-cn-hangzhou.aliyuncs.com/img/dataease-logo.png" alt="DataEase" width="300" /></a></p>
<h3 align="center">Alat BI (Business Intelligence) sumber terbuka yang simple dan mudah digunakan</h3>
<p align="center">
  <a href="https://www.gnu.org/licenses/gpl-3.0.html"><img src="https://img.shields.io/github/license/dataease/dataease?color=%231890FF" alt="License: GPL v3"></a>
  <a href="https://app.codacy.com/gh/dataease/dataease?utm_source=github.com&utm_medium=referral&utm_content=dataease/dataease&utm_campaign=Badge_Grade_Dashboard"><img src="https://app.codacy.com/project/badge/Grade/da67574fd82b473992781d1386b937ef" alt="Codacy"></a>
  <a href="https://github.com/dataease/dataease"><img src="https://img.shields.io/github/stars/dataease/dataease?color=%231890FF&style=flat-square" alt="GitHub Stars"></a>
  <a href="https://github.com/dataease/dataease/releases"><img src="https://img.shields.io/github/v/release/dataease/dataease" alt="GitHub release"></a>
</p>
<p align="center">
  <a href="/README.md"><img alt="中文(简体)" src="https://img.shields.io/badge/中文(简体)-d9d9d9"></a>
  <a href="/docs/README.en.md"><img alt="English" src="https://img.shields.io/badge/English-d9d9d9"></a>
  <a href="/docs/README.zh-Hant.md"><img alt="中文(繁體)" src="https://img.shields.io/badge/中文(繁體)-d9d9d9"></a>
  <a href="/docs/README.ja.md"><img alt="日本語" src="https://img.shields.io/badge/日本語-d9d9d9"></a>
  <a href="/docs/README.pt-br.md"><img alt="Português (Brasil)" src="https://img.shields.io/badge/Português (Brasil)-d9d9d9"></a>
  <a href="/docs/README.ar.md"><img alt="العربية" src="https://img.shields.io/badge/العربية-d9d9d9"></a>
  <a href="/docs/README.de.md"><img alt="Deutsch" src="https://img.shields.io/badge/Deutsch-d9d9d9"></a>
  <a href="/docs/README.es.md"><img alt="Español" src="https://img.shields.io/badge/Español-d9d9d9"></a>
  <a href="/docs/README.fr.md"><img alt="français" src="https://img.shields.io/badge/français-d9d9d9"></a>
  <a href="/docs/README.ko.md"><img alt="한국어" src="https://img.shields.io/badge/한국어-d9d9d9"></a>
  <a href="/docs/README.id.md"><img alt="Bahasa Indonesia" src="https://img.shields.io/badge/Bahasa Indonesia-d9d9d9"></a>
  <a href="/docs/README.tr.md"><img alt="Türkçe" src="https://img.shields.io/badge/Türkçe-d9d9d9"></a>
</p>

------------------------------

## Apa itu DataEase?

DataEase adalah alat BI sumber terbuka yang dirancang untuk membantu pengguna dengan cepat menganalisis data dan mendapatkan wawasan bisnis, memungkinkan mereka untuk meningkatkan dan mengoptimalkan operasi mereka. Itu mendukung berbagai macam sumber data, memungkinkan pengguna untuk membuat bagan dengan antarmuka drag-and-drop yang simple dan berbagi dengan mudah.

**Keunggulan DataEase:**

-   Sumber Terbuka: Tidak ada hambatan, pengadaan dan pemasangan online yang cepat, pembaruan bulanan.
-   Mudah Digunakan: Mudah digunakan; analisis dapat diselesaikan dengan klik mouse yang simple dan tindakan drag-and-drop.
-   Beragam: Mendukung pemasangan multi-platform dan opsi penyisipan yang beragam.
-   Berbagi yang Aman: Menawarkan berbagai metode berbagi data sambil memastikan keamanan data.

**Sumber Data yang Didukung:**

-   Database OLTP: MySQL, Oracle, SQL Server, PostgreSQL, MariaDB, Db2, TiDB, MongoDB-BI, dll.
-   Database OLAP: ClickHouse, Apache Doris, Apache Impala, StarRocks, dll.
-   Warehouse/Data Lake: Amazon RedShift, dll.
-   File Data: Excel, CSV, dll.
-   Sumber Data API.

## Panduan Cepat Memulai

```
# Prepare a Linux server with at least 2 CPUs and 4GB of RAM, then run the following one-click installation script as the root user:

curl -sSL https://dataease.oss-cn-hangzhou.aliyuncs.com/quick_start_v2.sh | bash

# Username: admin
# Password: DataEase@123456
```
## Teknologi Stack

-   Frontend: [Vue.js](https://vuejs.org/), [Element](https://element.eleme.cn/)
-   Pustaka Visualisasi: [AntV](https://antv.vision/zh)
-   Backend: [Spring Boot](https://spring.io/projects/spring-boot)
-   Database: [MySQL](https://www.mysql.com/)
-   Pemrosesan Data: [Apache Calcite](https://github.com/apache/calcite/), [Apache SeaTunnel](https://github.com/apache/seatunnel)
-   Infrastruktur: [Docker](https://www.docker.com/)

## Informasi Keamanan

Jika Anda menemukan masalah keamanan, silakan hubungi kami melalui: <EMAIL>.

## Lisensi

Hak cipta (c) 2014-2024 FIT2CLOUD, Hak cipta dilindungi undang-undang.

Dilisensikan di bawah Lisensi Publik Umum GNU versi 3 (GPLv3) ("Lisensi"); Anda tidak boleh menggunakan file ini kecuali dalam ketentuan Lisensi. Anda dapat mendapatkan salinan Lisensi di

https://www.gnu.org/licenses/gpl-3.0.html

Kecuali sesuai dengan undang-undang yang berlaku atau disepakati secara tertulis, perangkat lunak yang didistribusikan di bawah Lisensi didistribusikan pada DASAR "SEBENAR-BENAR", TANPA JAMINAN ATAU SYARAT APA PUN, baik yang diucapkan atau diimplikasikan. Lihat Lisensi untuk bahasa spesifik yang menentukan izin dan batasan di bawah Lisensi.
